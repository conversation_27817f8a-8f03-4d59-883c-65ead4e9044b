version: '2.2'

services:
  abus_portal2024_db:
    extends:
      file: common.yml
      service: abus_portal2024_db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: mariapwd

  abus_portal2024_php-fpm:
    extends:
      file: common.yml
      service: abus_portal2024_php-fpm
    depends_on:
      - abus_portal2024_redis
    restart: always
    build:
      args:
        phpini: php_prod.ini
    links:
      - abus_portal2024_db

  abus_portal2024_nginx:
    extends:
      file: common.yml
      service: abus_portal2024_nginx
    restart: always
    depends_on:
      - abus_portal2024_php-fpm
    volumes:
      - /etc/ssl/abus-bkp/chain.abus-kransysteme.de.crt:/etc/nginx/ssl/abus-kransysteme.de.crt
      - /etc/ssl/abus-bkp/abus-kransysteme.de.key:/etc/nginx/ssl/abus-kransysteme.de.key
    labels:
      - "traefik.http.routers.portal2024-secure.rule=Host(`portal2024.abus-kransysteme.de`)"
#      - "traefik.http.routers.portal2024-secure.rule=Host(`portal2024.abus-kransysteme.de`,`abukonfis2024.abus-kransysteme.de`,`ffd2024.abus-kransysteme.de`,`vital2024.abus-kransysteme.de`,`servicedoc2024.abus-kransysteme.de`,`tdata2024.abus-kransysteme.de`,`abukonfis20242.abus-kransysteme.de`)"
      - "traefik.http.routers.portal2024-secure.tls.certresolver=leresolver"
    links:
      - abus_portal2024_php-fpm

  abus_portal2024_redis:
    extends:
      file: common.yml
      service: abus_portal2024_redis
    restart: always

networks:
  portal2024-net:
    external: true
  traefik-net:
    external: true
  database-net:
    external: true
