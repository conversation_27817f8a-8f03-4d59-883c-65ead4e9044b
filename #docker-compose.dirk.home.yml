version: '2.2'

services:
  abus_portal2024_db:
    extends:
      file: common.yml
      service: abus_portal2024_db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: mariapwd

  abus_portal2024_php-fpm:
    extends:
      file: common.yml
      service: abus_portal2024_php-fpm
    depends_on:
      - abus_portal2024_redis
    restart: always
    build:
      args:
        development: 1
        http_proxy: http://*********:3128
        https_proxy: http://*********:3128
        phpini: php_dev_dirk.ini
    environment:
      XDEBUG_CONFIG: "client_host=*********"
      PHP_IDE_CONFIG: serverName=portal
      http_proxy: http://*********:3128
      https_proxy: http://*********:3128
    links:
      - abus_portal2024_db

  abus_portal2024_nginx:
    extends:
      file: common.yml
      service: abus_portal2024_nginx
    restart: always
    depends_on:
      - abus_portal2024_php-fpm
    build:
      args:
        development: 1
        nginxconf: nginx_dev_dirk.conf
        http_proxy: http://*********:3128
        https_proxy: http://*********:3128
    labels:
      - "traefik.http.routers.portal2024-secure.rule=Host(`portal2024.development.dirk`,`abukonfis2024.development.dirk`,`ffd2024.development.dirk`,`vital2024.development.dirk`,`servicedoc2024.development.dirk`,`tdata2024.development.dirk`,`abukonfis20242.development.dirk`)"
    links:
      - abus_portal2024_php-fpm

  abus_portal2024_redis:
    extends:
      file: common.yml
      service: abus_portal2024_redis
    restart: always

networks:
  portal2024-net:
    external: true
  traefik-net:
    external: true
  database-net:
    external: true
