worker_processes 1;

user www-data www-data;

events {
  worker_connections 1024;
}

http {

  # Hide nginx version information.
  server_tokens off;

  # Define the MIME types for files.
  include       /etc/nginx/mime.types;
  default_type  application/octet-stream;

  # Update charset_types due to updated mime.types
  charset_types text/xml text/plain text/vnd.wap.wml application/x-javascript application/rss+xml text/css application/javascript application/json;

  # Format to use in log files
  log_format  main  '$http_x_forwarded_for - $remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent"';

  # How long to allow each connection to stay idle; longer values are better
  # for each individual client, particularly for SSL, but means that worker
  # connections are tied up longer. (Default: 65)
  keepalive_timeout 600;

  # Speed up file transfers by using sendfile() to copy directly
  # between descriptors rather than using read()/write().
  sendfile        on;

  # Tell Nginx not to send out partial frames; this increases throughput
  # since TCP frames are filled up before being sent out. (adds TCP_CORK)
  tcp_nopush      on;

  # Tell Nginx to enable the Nagle buffering algorithm for TCP packets, which
  # collates several smaller packets together into one larger packet, thus saving
  # bandwidth at the cost of a nearly imperceptible increase to latency. (removes TCP_NODELAY)
  tcp_nodelay     off;

  # Compression

  # Enable Gzip compressed.
  gzip on;

  # Enable compression both for HTTP/1.0 and HTTP/1.1 (required for CloudFront).
  gzip_http_version  1.0;

  # Compression level (1-9).
  # 5 is a perfect compromise between size and cpu usage, offering about
  # 75% reduction for most ascii files (almost identical to level 9).
  gzip_comp_level    5;

  # Don't compress anything that's already small and unlikely to shrink much
  # if at all (the default is 20 bytes, which is bad as that usually leads to
  # larger files after gzipping).
  gzip_min_length    256;

  # Compress data even for clients that are connecting to us via proxies,
  # identified by the "Via" header (required for CloudFront).
  gzip_proxied       any;

  # Tell proxies to cache both the gzipped and regular version of a resource
  # whenever the client's Accept-Encoding capabilities header varies;
  # Avoids the issue where a non-gzip capable client (which is extremely rare
  # today) would display gibberish if their proxy gave them the gzipped version.
  gzip_vary          on;

  # Compress all output labeled with one of the following MIME-types.
  gzip_types
    application/atom+xml
    application/javascript
    application/json
    application/rss+xml
    application/vnd.ms-fontobject
    application/x-font-ttf
    application/x-javascript
    application/x-web-app-manifest+json
    application/xhtml+xml
    application/xml
    font/opentype
    image/svg+xml
    image/x-icon
    text/css
    text/javascript
    text/plain
    text/x-component
    text/x-javascript;
  # text/html is always compressed by HttpGzipModule

    proxy_buffer_size 128k;
    proxy_buffers 4 256k;
    proxy_busy_buffers_size 256k;

    fastcgi_buffer_size 128k;
    fastcgi_buffers 4 256k;
    fastcgi_busy_buffers_size 256k;

    proxy_cache_path /tmp/nginx/cache/abukonfis/  keys_zone=auth_cache:10m;
    proxy_cache_path /tmp/nginx/cache/servicedoc/  keys_zone=auth_cache_servicedoc:10m;

    # ABUKONFIS Application Server
    # The ngx_http_upstream_module module is used to define groups of servers that can be referenced by the proxy_pass directives.
    upstream applicationserver {
        server **************;
    }

    server {

        listen 80;
        server_name abukonfis.development.mario;

        #ssl on;
        #ssl_certificate /etc/nginx/ssl/development.mario.crt;
        #ssl_certificate_key /etc/nginx/ssl/development.mario.key;

        add_header X-Content-Security-Policy "frame-ancestors https://*.development.mario";
        add_header Content-Security-Policy "frame-ancestors https://*.development.mario";

        location / {

            # Berechtigung anfragen
            #
            # The ngx_http_auth_request_module module (1.5.4+) implements client authorization based on the result of a subrequest.
            # If the subrequest returns a 2xx response code, the access is allowed.
            # If it returns 401 or 403, the access is denied with the corresponding error code.
            # Any other response code returned by the subrequest is considered an error.
            auth_request /auth;

            # redirect 401 to login form
            error_page 401 =200 /login;

            # redirect 200 to ABUKONFIS Application Server
            proxy_pass http://applicationserver;
            #proxy_cookie_domain ************** abukonfis.development.mario;
        }

        location /login {
            rewrite ^ https://portal.development.mario/login permanent;
        }

        location /auth {
            # Specifies that a given location can only be used for internal requests.
            # For external requests, the client error 404 (Not Found) is returned.
            internal;

            # Sets the protocol and address of a proxied server and an optional URI to which a location should be mapped.
            proxy_pass https://portal_nginx/abukauth;
            proxy_redirect https://portal_nginx/abukauth /auth;
            proxy_pass_request_body off;
            proxy_set_header Content-Length "";
            proxy_set_header Host portal.development.mario;
            proxy_ignore_headers X-Accel-Expires Cache-Control Set-Cookie Expires Vary;
            proxy_hide_header X-Accel-Expires;
            proxy_hide_header Cache-Control;
            proxy_hide_header Set-Cookie;
            proxy_hide_header Expires;
            proxy_hide_header Vary;

            proxy_set_header Cookie ABUSPORTAL=$cookie_ABUSPORTAL;

            # Der Proxy Cache cached die Authentifizierung für 1 Minute, damit die Ressource wie CSS/JS/IMAGES geladen werden können,
            # ohne jedesmal die Authentifizierung auszuführen
            proxy_cache auth_cache;
            proxy_cache_valid 200 1m;
            proxy_cache_key "$cookie_ABUSPORTAL";
        }

        error_log /var/log/nginx/error_abukonfis.log;
        access_log /var/log/nginx/access_abukonfis.log main;
    }

    server {

        listen 80;
        server_name servicedoc.development.mario;

        #ssl on;
        #ssl_certificate /etc/nginx/ssl/development.mario.crt;
        #ssl_certificate_key /etc/nginx/ssl/development.mario.key;

        add_header X-Content-Security-Policy "frame-ancestors https://*.development.mario";
        add_header Content-Security-Policy "frame-ancestors https://*.development.mario";

        root /var/www/servicedoc;
        index index.html;

        location / {

            # Berechtigung anfragen
            #
            # The ngx_http_auth_request_module module (1.5.4+) implements client authorization based on the result of a subrequest.
            # If the subrequest returns a 2xx response code, the access is allowed.
            # If it returns 401 or 403, the access is denied with the corresponding error code.
            # Any other response code returned by the subrequest is considered an error.
            auth_request /auth;

            # redirect 401 to login form
            error_page 401 =200 /login;
        }

        location /login {
            rewrite ^ https://portal.development.mario/login permanent;
        }

        location /auth {
            # Specifies that a given location can only be used for internal requests.
            # For external requests, the client error 404 (Not Found) is returned.
            internal;

            # Sets the protocol and address of a proxied server and an optional URI to which a location should be mapped.
            proxy_pass https://portal_nginx/documentation/service/auth;
            proxy_redirect https://portal_nginx/documentation/service/auth /auth;
            proxy_pass_request_body off;
            proxy_set_header Content-Length "";
            proxy_set_header Host portal.development.mario;
            proxy_ignore_headers X-Accel-Expires Cache-Control Set-Cookie Expires Vary;
            proxy_hide_header X-Accel-Expires;
            proxy_hide_header Cache-Control;
            proxy_hide_header Set-Cookie;
            proxy_hide_header Expires;
            proxy_hide_header Vary;

            proxy_set_header Cookie ABUSportal=$cookie_ABUSportal;

            # Der Proxy Cache cached die Authentifizierung für 1 Minute, damit die Ressource wie CSS/JS/IMAGES geladen werden können,
            # ohne jedesmal die Authentifizierung auszuführen
            proxy_cache auth_cache_servicedoc;
            proxy_cache_valid 200 1m;
            proxy_cache_key "$cookie_ABUSportal";
        }


        error_log /var/log/nginx/error_servicedoc.log;
        access_log /var/log/nginx/access_servicedoc.log main;
    }

    server {

        listen 80;
        server_name portal.development.mario ffd.development.mario vital.development.mario;

        client_max_body_size 64M;

        #ssl on;
        #ssl_certificate /etc/nginx/ssl/development.mario.crt;
        #ssl_certificate_key /etc/nginx/ssl/development.mario.key;

        root /var/www/html/public;

        keepalive_timeout 120;
        types_hash_max_size 2048;

        # strip trailling slashes
        rewrite ^/(.*)/$ /$1 permanent;

        location / {
            # try to serve file directly, fallback to rewrite
	        try_files $uri /index.php$is_args$args;
        }

        location ~ ^/index\.php(/|$) {
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass portal_php-fpm:9000;
            fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
            fastcgi_param DOCUMENT_ROOT $realpath_root;
            include fastcgi_params;
            fastcgi_read_timeout 600;
            fastcgi_buffers 4 256k;
            fastcgi_buffer_size 128k;
            fastcgi_busy_buffers_size 256k;
            fastcgi_param ENVIRONMENT dev;
        }

        # return 404 for all other php files not matching the front controller
        # this prevents access to other php files you don't want to be accessible.
        location ~ \.php$ {
            return 404;
        }

        error_log /var/log/nginx/error.log;
        access_log /var/log/nginx/access.log main;
        }
}
