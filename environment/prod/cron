### ABUS BOX
15 13 * * * root /usr/local/bin/php /var/www/html/bin/console box:nextcloudConfigParser >> /var/log/cron/cron.log 2>&1
*/10 * * * * root /usr/local/bin/php /var/www/html/bin/console box:nextcloudTempCleaner >> /var/log/cron/cron.log 2>&1
0 5 * * * root /usr/local/bin/php /var/www/html/bin/console box:nextcloudElasticsearch >> /var/log/cron/cron.log 2>&1
30 6 * * 0 root /usr/local/bin/php /var/www/html/bin/console box:nextcloudConfigParser >> /var/log/cron/cron.log 2>&1
45 6 * * 0 root /usr/local/bin/php /var/www/html/bin/console box:nextcloudElasticsearch >> /var/log/cron/cron.log 2>&1

### APIS
1 5 * * * root /usr/local/bin/php /var/www/html/bin/console apis:publish >> /var/log/cron/cron.log 2>&1

### CAD Exchange
*/10 * * * * root /usr/local/bin/php /var/www/html/bin/console cadExchange:crawl >> /var/log/cron/cadexchange.log 2>&1

### Redis
*/5 * * * * root /usr/local/bin/php /var/www/html/bin/console api:ldap2redis >> /var/log/cron/cron.log 2>&1

### Taskrunner
*/1 * * * * root /usr/local/bin/php /var/www/html/bin/console taskrunner:run:all >> /var/log/cron/cron.log 2>&1

### Technical Data
0 0 * * * root /usr/local/bin/php /var/www/html/bin/console technicalData:dailyMail >> /var/log/cron/cron.log 2>&1
0 0 * * * root /usr/local/bin/php /var/www/html/bin/console technicalData:dailyMailViaABUKonfis >> /var/log/cron/cron.log 2>&1
