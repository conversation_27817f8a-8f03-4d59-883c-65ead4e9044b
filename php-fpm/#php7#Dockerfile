FROM php:7.4.11-fpm

MAINTAINER <PERSON> <<EMAIL>>

ARG environment
#ARG http_proxy
#ARG https_proxy
ARG xdebug_client_port
ARG xdebug_client_host

#ENV http_proxy="$http_proxy"
#ENV https_proxy="$https_proxy"
ENV PHP_AUTOCONF="/usr/bin/autoconf"

### FOR DEVELOPMENT ONLY
ARG development
#ARG useproxy
#RUN if [ "$useproxy" -eq "1" ]; then pear config-set http_proxy $http_proxy; fi
#RUN if [ "$useproxy" -eq "1" ]; then echo "Acquire::http::Pipeline-Depth 0;" >> /etc/apt/apt.conf.d/99fixbadproxy; fi
#RUN if [ "$useproxy" -eq "1" ]; then echo 'Acquire::http::No-Cache true;' >> /etc/apt/apt.conf.d/99fixbadproxy; fi
#RUN if [ "$useproxy" -eq "1" ]; then echo "Acquire::BrokenProxy true;" >> /etc/apt/apt.conf.d/99fixbadproxy; fi


# Install additional software
RUN apt-get update && apt-get install -y \
        git \
        zip \
        zlib1g-dev \
        libicu-dev \
        g++ \
        libldap2-dev \
        libxml2-dev \
        libxslt1-dev \
        libbz2-dev \
        libcurl4-openssl-dev \
        file \
        libssl-dev \
        libonig-dev \
        libzip-dev \
        wget \
        ldap-utils \
        cron \
        graphviz \
        autoconf \
        g++ \
        make \
        build-essential \
    && CFLAGS="-I/usr/src/php" docker-php-ext-install xmlreader \
    && docker-php-ext-configure ldap --with-libdir=lib/x86_64-linux-gnu/ \
    && docker-php-ext-configure intl \
    && docker-php-ext-install -j$(nproc) \
        iconv \
        opcache \
        pdo \
        pdo_mysql \
        mysqli \
        ldap \
        intl \
        json \
        xsl \
        simplexml \
        posix \
        mbstring \
        bz2 \
        curl \
        dom \
        fileinfo \
        ftp \
        pcntl \
        zip \
&& apt-get autoremove -y \
#    && apt-get remove -y autoconf automake libtool nasm make pkg-config libz-dev build-essential g++ \
    && apt-get clean; rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /usr/share/doc/* ~/.composer \
&& echo "Fertig"

# REDIS
RUN pecl channel-update pecl.php.net && pecl install -o -f redis && docker-php-ext-enable redis

# Installation Imagemagick
RUN apt-get update && apt-get install -y libmagickwand-dev --no-install-recommends && rm -rf /var/lib/apt/lists/*
RUN pecl install imagick && docker-php-ext-enable imagick

# PHP konfigurieren
COPY environment/$environment/php.ini /usr/local/etc/php/conf.d/abus.ini

# LDAP konfigurieren
COPY php-fpm/config/ldap.conf /etc/ldap/ldap.conf
RUN chmod 774 /etc/ldap/ldap.conf
COPY php-fpm/config/abus_ldap.cert abus_ldap.cert
RUN cat abus_ldap.cert >> /etc/ssl/certs/ca-certificates.crt
RUN rm abus_ldap.cert
RUN chmod 774 /etc/ssl/certs/ca-certificates.crt

# Browscap konfigurieren
COPY php-fpm/config/php_browscap.ini /usr/local/etc/php/php_browscap.ini

# ssh2 module
#RUN yes | pecl install ssh2 \
#  && echo "extension=ssh2.so" > /usr/local/etc/php/conf.d/ext-ssh2.ini
#RUN cd /tmp && wget https://pecl.php.net/get/ssh2-1.3.1.tgz && tar -xzf ssh2-1.3.1.tgz && cd /tmp/ssh2-1.3.1 \
#  && phpize && ./configure && make && make install \
#  && echo "extension=ssh2.so" > /usr/local/etc/php/conf.d/ext-ssh2.ini \
#  && rm -rf /tmp/ssh2

## NPM installieren
#RUN apt-get update && apt-get install -my wget gnupg
#RUN curl -sL https://deb.nodesource.com/setup_12.x | bash -
#RUN apt-get install -y nodejs

# GRUNT und BOWER installieren
#RUN if [ "$development" -eq "1" ]; then npm config set proxy $http_proxy && npm config set https-proxy $https_proxy; fi
#RUN npm install -g grunt-cli && npm install -g bower

### FOR DEVELOPMENT ONLY | Install xDebug
#RUN if [ "$development" -eq "1" ]; then yes | pecl install xdebug && mkdir /var/log/xdebug && echo "zend_extension=$(find /usr/local/lib/php/extensions/ -name xdebug.so)" > /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.default_enable=0" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.remote_enable=1" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.remote_autostart=0" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.profiler_enable=0" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.profiler_output_dir=/var/log/xdebug" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.remote_connect_back=0" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.remote_port=9000" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.idekey=PHPSTORM" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.max_nesting_level=1000" >> /usr/local/etc/php/conf.d/xdebug.ini; fi
# Install xdebug
RUN if [ "$development" -eq "1" ]; then yes | pecl channel-update pecl.php.net && pecl install xdebug-3.1.6 \
    && mkdir /var/log/xdebug \
    && touch /var/log/xdebug/remote.log \
    && chmod 777 /var/log/xdebug/remote.log \
    && echo "zend_extension=$(find /usr/local/lib/php/extensions/ -name xdebug.so)" > /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.mode=debug" >> /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.client_port=$xdebug_client_port" >> /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.client_host=$xdebug_client_host" >> /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.idekey=PHPSTORM" >> /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.max_nesting_level=1000" >> /usr/local/etc/php/conf.d/xdebug.ini; fi


#RUN if [ "$development" -eq "1" ]; then yes | pecl install xdebug \
#    && mkdir /var/log/xdebug \
#    && touch /var/log/xdebug/remote.log \
#    && chmod 777 /var/log/xdebug/remote.log \
#    && echo "zend_extension=$(find /usr/local/lib/php/extensions/ -name xdebug.so)" > /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.default_enable=0" >> /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.remote_enable=1" >> /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.remote_autostart=0" >> /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.profiler_enable=0" >> /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.profiler_output_dir=/var/log/xdebug" >> /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.remote_log=/var/log/xdebug/remote.log" >> /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.remote_connect_back=0" >> /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.remote_port=9000" >> /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.idekey=PHPSTORM" >> /usr/local/etc/php/conf.d/xdebug.ini \
#    && echo "xdebug.max_nesting_level=1000" >> /usr/local/etc/php/conf.d/xdebug.ini; fi

# Add timezone
RUN rm /etc/localtime && \
    ln -s /usr/share/zoneinfo/UTC /etc/localtime && \
    "date"

# Install composer
RUN curl -sS https://getcomposer.org/installer | \
    php -- --install-dir=/usr/local/bin --filename=composer

# Add CRONJOB
RUN mkdir /var/log/cron && chmod -R 777 /var/log/cron
COPY environment/$environment/cron /etc/cron.d/cron
RUN chmod 0644 /etc/cron.d/cron
RUN crontab /etc/cron.d/cron

COPY php-fpm/entrypoint.sh /
RUN chmod 777 /entrypoint.sh
ENTRYPOINT /entrypoint.sh


#RUN sed -i '/#!\/bin\/sh/a \umask 0000' /usr/local/bin/docker-php-entrypoint

#RUN if [ "$development" -eq "1" ]; then adduser --no-create-home --ingroup www-data $user; fi

#USER www-data
