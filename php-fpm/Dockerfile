FROM php:8.3.19-fpm

MAINTAINER <PERSON> <<EMAIL>>

ARG environment
ARG xdebug_client_port
ARG xdebug_client_host

### FOR DEVELOPMENT ONLY
ARG development

# Install additional software
RUN apt-get update && apt-get install -y \
        git \
        zip \
        zlib1g-dev \
        libicu-dev \
        g++ \
        libldap2-dev \
        libxml2-dev \
        libxslt1-dev \
        libbz2-dev \
        libcurl4-openssl-dev \
        file \
        libssl-dev \
        libonig-dev \
        libzip-dev \
        wget \
        ldap-utils \
        wkhtmltopdf \
        cron \
        graphviz \
    && CFLAGS="-I/usr/src/php" docker-php-ext-install xmlreader \
    && docker-php-ext-configure ldap --with-libdir=lib/x86_64-linux-gnu/ \
    && docker-php-ext-configure intl \
    && docker-php-ext-install -j$(nproc) \
        iconv \
        opcache \
        pdo \
        pdo_mysql \
        mysqli \
        ldap \
        intl \
        json \
        xsl \
        simplexml \
        posix \
        mbstring \
        bz2 \
        curl \
        dom \
        fileinfo \
        ftp \
        pcntl \
        zip \
&& apt-get autoremove -y \
    && apt-get remove -y autoconf automake libtool nasm make pkg-config libz-dev build-essential g++ \
    && apt-get clean; rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /usr/share/doc/* ~/.composer \
&& echo "Fertig"

# Installiere Imagick über PECL und aktiviere es
ARG IMAGICK_VERSION=3.7.0

# Imagick is installed from the archive because regular installation fails
# See: https://github.com/Imagick/imagick/issues/643#issuecomment-1834361716
RUN apt-get update && apt-get install -y \
        libmagickwand-dev \
    && curl -L -o /tmp/imagick.tar.gz https://github.com/Imagick/imagick/archive/refs/tags/${IMAGICK_VERSION}.tar.gz \
    && tar --strip-components=1 -xf /tmp/imagick.tar.gz \
    && phpize \
    && ./configure \
    && make \
    && make install \
    && echo "extension=imagick.so" > /usr/local/etc/php/conf.d/ext-imagick.ini \
    && rm -rf /tmp/*

# REDIS
RUN pecl install redis && docker-php-ext-enable redis

# PHP konfigurieren
COPY environment/$environment/php.ini /usr/local/etc/php/conf.d/abus.ini

# LDAP konfigurieren
COPY php-fpm/config/ldap.conf /etc/ldap/ldap.conf
RUN chmod 774 /etc/ldap/ldap.conf
COPY php-fpm/config/abus_ldap.cert abus_ldap.cert
RUN cat abus_ldap.cert >> /etc/ssl/certs/ca-certificates.crt
RUN rm abus_ldap.cert
RUN chmod 774 /etc/ssl/certs/ca-certificates.crt

# Browscap konfigurieren
COPY php-fpm/config/php_browscap.ini /usr/local/etc/php/php_browscap.ini

# Install xdebug
RUN if [ "$development" -eq "1" ]; then yes | pecl install xdebug-3.4.2 \
    && mkdir /var/log/xdebug \
    && touch /var/log/xdebug/remote.log \
    && chmod 777 /var/log/xdebug/remote.log \
    && echo "zend_extension=$(find /usr/local/lib/php/extensions/ -name xdebug.so)" > /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.mode=debug" >> /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.client_port=$xdebug_client_port" >> /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.client_host=$xdebug_client_host" >> /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.idekey=PHPSTORM" >> /usr/local/etc/php/conf.d/xdebug.ini \
    && echo "xdebug.max_nesting_level=1000" >> /usr/local/etc/php/conf.d/xdebug.ini; fi

# Add timezone
RUN rm /etc/localtime && \
    ln -s /usr/share/zoneinfo/UTC /etc/localtime && \
    "date"

# Install composer
RUN curl -sS https://getcomposer.org/installer | \
    php -- --install-dir=/usr/local/bin --filename=composer

# Add CRONJOB
RUN mkdir /var/log/cron && chmod -R 777 /var/log/cron
COPY environment/$environment/cron /etc/cron.d/cron
RUN chmod 0644 /etc/cron.d/cron
RUN crontab /etc/cron.d/cron

COPY php-fpm/entrypoint.sh /
RUN chmod 777 /entrypoint.sh
ENTRYPOINT /entrypoint.sh