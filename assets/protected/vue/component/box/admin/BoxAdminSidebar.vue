<template>
  <div id="sideSearch" class="sidebar-panel">
    <h5 class="sidebar-panel-title">
      <i class="fas fa-cloud-download-alt mr5"></i>{{ $t('box/admin/administration') }}
    </h5>

    <div class="content">
      <button
        class="btn btn-sm btn-abus-blue-dark btn-block"
        type="button"
        @click="onCreateIndexTask({ path: '/', pdf: false })"
        v-html="$t('box/admin/index')"></button>
      <button
        class="btn btn-sm btn-danger btn-block"
        type="button"
        @click="onCreateIndexTask({ path: '/', pdf: true })"
        v-html="$t('box/admin/index_pdf')">
        {{ $t("box/admin/index_pdf") }}<br /><span style="font-size: 0.8em; font-weight: bold"
          >{{ $t("box/admin/takeAWhile") }}</span
        >
      </button>

      <div class="mt10">
        <i class="far fa-hdd mr5"></i>
        {{ $t('box/admin/diskSpace') }}: {{ datasize }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { store } from '@assets/protected/vue/store';
import ModalEventBus from 'EventBuses/modal';

// administration: 'Administration',
//     takeAWhile: 'This may take a while!',
//     diskSpace: 'Disk space',

type IndexTaskPayload = {
  path: string;
  pdf: boolean;
};

const datasize = ref<string>('');

function onCreateIndexTask(payload: IndexTaskPayload): void {
  store.dispatch('box/createIndexTask', payload);
}

async function getDataSize(): Promise<void> {
  const url = '/box/api/administration/getDataSize';

  try {
    const response = await axios.get(url);
    datasize.value = response.data;
  } catch (error: any) {
    ModalEventBus.$emit('showModal', {
      type: 'error',
      message: error.response?.data || 'Unknown error'
    });
  }
}

onMounted(() => {
  getDataSize();
});
</script>

<style lang="scss" scoped>
@import '@assets/public/vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables.scss';
</style>
