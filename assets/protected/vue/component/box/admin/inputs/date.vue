<template>
  <div class="row">
    <div class="label col-sm-3" v-html="$t(label)"></div>

    <div class="list col-sm-4">
      <div class="input-group">
        <div class="input-group-prepend">
          <div class="input-group-text">
            <i class="fa fa-calendar"></i>
          </div>
          <!-- TODO: Replace date-picker component -->
          <!--          <date-picker
            v-model="value"
            :config="config"
            :wrap="true"
            @dp-change="setValue({ inode: inode, category: category, setting: setting, value: value })"
            style="border-left: none; border-top-left-radius: 0; border-bottom-left-radius: 0"></date-picker>-->
        </div>
      </div>
    </div>

    <div class="description col-sm-5" v-html="$t(description)"></div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import Component from 'vue-class-component';
import { Prop } from 'vue-property-decorator';
import { Getter, Action } from 'vuex-class';

//import datePicker from '@node_modules/vue-bootstrap-datetimepicker/src/component.vue';
//import 'pc-bootstrap4-datetimepicker/build/css/bootstrap-datetimepicker.css';
import moment from 'moment';

import de from 'Translations/across/across.de_DE.json';
import en from 'Translations/across/across.en_GB.json';
import fr from 'Translations/across/across.fr_FR.json';
import es from 'Translations/across/across.es_ES.json';

import VueI18n from 'vue-i18n';
Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: <string>document.getElementsByTagName('html')[0].getAttribute('lang'),
  messages: { de, en, fr, es },
});

@Component({
  name: 'DateComponent',
  i18n,
  components: {},
})
export default class Date extends Vue {
  @Prop({}) label: string;
  @Prop({}) description: string;
  @Prop({}) inode: number;
  @Prop({}) category: string;
  @Prop({}) setting: string;

  @Getter('box/getValue') public getValue: (inode: number, category: string, setting: string) => any;
  @Action('box/setValue') setValue: any;

  value: string = '';

  created() {
    this.value = this.getValue(this.inode, this.category, this.setting);
  }

  config: any = {
    locale: moment.locale(<string>document.getElementsByTagName('html')[0].getAttribute('lang')),
    format: 'L',
    showClear: true,
    useCurrent: false,
    allowInputToggle: true,
    icons: {
      time: 'fa fa-clock-o',
      date: 'fa fa-calendar',
      up: 'fa fa-chevron-up',
      down: 'fa fa-chevron-down',
      previous: 'fa fa-chevron-left',
      next: 'fa fa-chevron-right',
      today: 'fa fa-sun-o',
      clear: 'fa fa-trash',
      close: 'fa fa-remove',
    },
  };
}
</script>

<style lang="scss" scoped>
.row {
  margin-bottom: 20px;

  .label {
    font-weight: bold;
    line-height: 30px;
  }

  .description {
    font-size: 0.8rem;
  }
}
</style>
