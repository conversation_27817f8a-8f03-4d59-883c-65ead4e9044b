<template>
  <div class="admin">
    <div class="card mb20">
      <div class="card-header bg-secondary text-white">Berechtigungen</div>
      <div class="card-body bg-white">
        <ThreeWaySwitch
          label="box/admin/permission/public"
          description="box/admin/permission/public/description"
          :inode="administration.file.inode"
          category="permission"
          setting="public"></ThreeWaySwitch>

        <hr />

        <ThreeWaySwitch
          label="box/admin/permission/clear_groups"
          description="box/admin/permission/clear_groups/description"
          :inode="administration.file.inode"
          category="permission"
          setting="clear_groups"></ThreeWaySwitch>

        <hr />

        <ThreeWaySwitch
          label="box/admin/permission/clear_users"
          description="box/admin/permission/clear_users/description"
          :inode="administration.file.inode"
          category="permission"
          setting="clear_users"></ThreeWaySwitch>

        <hr />

        <List
          label="box/admin/permission/allowed_groups"
          description="box/admin/permission/allowed_groups/description"
          :inode="administration.file.inode"
          category="permission"
          setting="allowed_groups"></List>

        <hr />

        <List
          label="box/admin/permission/allowed_users"
          description="box/admin/permission/allowed_users/description"
          :inode="administration.file.inode"
          category="permission"
          setting="allowed_users"></List>
      </div>
    </div>

    <div class="card mb20">
      <div class="card-header bg-secondary text-white">Spracheinstellungen</div>
      <div class="card-body bg-white">
        <List
          label="box/admin/languages/languages"
          description="box/admin/languages/languages/description"
          placeholder="box/admin/languages/languages/placeholder"
          :inode="administration.file.inode"
          category="language"
          setting="languages"></List>

        <hr />

        <Language-Selector
          label="box/admin/languages/default"
          description="box/admin/languages/default/description"
          :singleSelection="true"
          :inode="administration.file.inode"
          category="language"
          setting="default"></Language-Selector>

        <!--<Textfield-->
        <!--label="box/admin/languages/default"-->
        <!--description="box/admin/languages/default/description"-->
        <!--placeholder="box/admin/languages/default/placeholder"-->
        <!--:inode="administration.file.inode"-->
        <!--category="languages"-->
        <!--setting="default"-->
        <!--&gt;</Textfield>-->

        <hr />

        <Language-Selector
          label="box/admin/languages/only"
          description="box/admin/languages/only/description"
          :inode="administration.file.inode"
          category="language"
          setting="only"></Language-Selector>

        <!--<List-->
        <!--label="box/admin/languages/only"-->
        <!--description="box/admin/languages/only/description"-->
        <!--placeholder="box/admin/languages/only/placeholder"-->
        <!--:inode="administration.file.inode"-->
        <!--category="languages"-->
        <!--setting="only"-->
        <!--&gt;</List>-->

        <hr />

        <Textfield
          label="box/admin/language/foldernameDE"
          description="box/admin/language/foldername/description"
          :inode="administration.file.inode"
          category="language"
          setting="foldernameDE"></Textfield>

        <Textfield
          label="box/admin/language/foldernameEN"
          description=""
          :inode="administration.file.inode"
          category="language"
          setting="foldernameEN"></Textfield>

        <Textfield
          label="box/admin/language/foldernameFR"
          description=""
          :inode="administration.file.inode"
          category="language"
          setting="foldernameFR"></Textfield>

        <Textfield
          label="box/admin/language/foldernameES"
          description=""
          :inode="administration.file.inode"
          category="language"
          setting="foldernameES"></Textfield>
      </div>
    </div>

    <div class="card mb20">
      <div class="card-header bg-secondary text-white">Sucheinstellungen</div>
      <div class="card-body bg-white">
        <!--<ThreeWaySwitch-->
        <!--label="box/admin/search/searchable"-->
        <!--description="box/admin/search/searchable/description"-->
        <!--:inode="administration.file.inode"-->
        <!--category="search"-->
        <!--setting="searchable"-->
        <!--&gt;</ThreeWaySwitch>-->

        <!--<hr>-->

        <!--<ThreeWaySwitch-->
        <!--label="box/admin/search/searchFileExtension"-->
        <!--description="box/admin/search/searchFileExtension/description"-->
        <!--:inode="administration.file.inode"-->
        <!--category="search"-->
        <!--setting="searchFileExtension"-->
        <!--&gt;</ThreeWaySwitch>-->

        <!--<hr>-->

        <List
          label="box/admin/search/tags"
          description="box/admin/search/tags/description"
          placeholder="box/admin/search/tags/placeholder"
          :inode="administration.file.inode"
          category="search"
          setting="tags"></List>
      </div>
    </div>

    <div class="card mb20">
      <div class="card-header bg-secondary text-white">Anzeigeeinstellungen</div>
      <div class="card-body bg-white">
        <!--<ThreeWaySwitch-->
        <!--label="box/admin/display/hideFromList"-->
        <!--description="box/admin/display/hideFromList/description"-->
        <!--:inode="administration.file.inode"-->
        <!--category="display"-->
        <!--setting="hideFromList"-->
        <!--&gt;</ThreeWaySwitch>-->

        <!--<hr>-->

        <!--<ThreeWaySwitch-->
        <!--label="box/admin/display/listContent"-->
        <!--description="box/admin/display/listContent/description"-->
        <!--:inode="administration.file.inode"-->
        <!--category="display"-->
        <!--setting="listContent"-->
        <!--&gt;</ThreeWaySwitch>-->

        <!--<hr>-->

        <ThreeWaySwitch
          label="box/admin/display/downloadable"
          description="box/admin/display/downloadable/description"
          :inode="administration.file.inode"
          category="display"
          setting="downloadable"></ThreeWaySwitch>

        <hr />

        <!--<ThreeWaySwitch-->
        <!--label="box/admin/display/copyLink"-->
        <!--description="box/admin/display/copyLink/description"-->
        <!--:inode="administration.file.inode"-->
        <!--category="display"-->
        <!--setting="copyLink"-->
        <!--&gt;</ThreeWaySwitch>-->

        <!--<hr>-->

        <ThreeWaySwitch
          label="box/admin/display/fileExtension"
          description="box/admin/display/fileExtension/description"
          :inode="administration.file.inode"
          category="display"
          setting="fileExtension"></ThreeWaySwitch>

        <hr />

        <ThreeWaySwitch
          label="box/admin/display/fileDate"
          description="box/admin/display/fileDate/description"
          :inode="administration.file.inode"
          category="display"
          setting="fileDate"></ThreeWaySwitch>

        <hr />

        <!--<ThreeWaySwitch-->
        <!--label="box/admin/display/filenameVersion"-->
        <!--description="box/admin/display/filenameVersion/description"-->
        <!--:inode="administration.file.inode"-->
        <!--category="display"-->
        <!--setting="filenameVersion"-->
        <!--&gt;</ThreeWaySwitch>-->

        <!--<hr>-->

        <!--<ThreeWaySwitch-->
        <!--label="box/admin/display/metaVersion"-->
        <!--description="box/admin/display/metaVersion/description"-->
        <!--:inode="administration.file.inode"-->
        <!--category="display"-->
        <!--setting="metaVersion"-->
        <!--&gt;</ThreeWaySwitch>-->

        <!--<hr>-->

        <ThreeWaySwitch
          label="box/admin/display/MP4Video"
          description="box/admin/display/MP4Video/description"
          :inode="administration.file.inode"
          category="display"
          setting="MP4Video"></ThreeWaySwitch>

        <hr />

        <ThreeWaySwitch
          label="box/admin/display/isArchived"
          description="box/admin/display/isArchived/description"
          :inode="administration.file.inode"
          category="display"
          setting="isArchived"></ThreeWaySwitch>

        <hr />

        <Date
          label="box/admin/display/from"
          description="box/admin/display/from/description"
          :inode="administration.file.inode"
          category="display"
          setting="from"></Date>

        <hr />

        <Date
          label="box/admin/display/until"
          description="box/admin/display/until/description"
          :inode="administration.file.inode"
          category="display"
          setting="until"></Date>
      </div>
    </div>

    <div class="card mb20">
      <div class="card-header bg-secondary text-white">Meta</div>
      <div class="card-body bg-white">
        <Textfield
          label="box/admin/meta/version"
          description="box/admin/meta/version/description"
          :inode="administration.file.inode"
          category="meta"
          setting="version"></Textfield>
      </div>
    </div>

    <div class="yaml">
      <pre v-html="getYaml(administration.file.inode)"></pre>
    </div>

    <div class="mt20">
      <span class="lastIndexed" v-html="$t('box/admin/last_indexed') + ' ' + lastindexeddate"></span>
    </div>

    <hr />

    <!--<b-card no-body>-->
    <!--<b-tabs card>-->

    <!--<b-tab title="Übersicht" active>-->

    <!--<div>-->
    <!--<ul>-->
    <!--<li v-for="(value, key) in administration.overview">-->
    <!--{{ key }}-->
    <!--<ul>-->
    <!--<li v-for="(value, key) in value">-->
    <!--{{ key }}-->

    <!--<ul v-if="value.isArray">-->
    <!--<li v-for="(value, key) in value.value">-->
    <!--{{ value.name }}-->
    <!--</li>-->
    <!--</ul>-->
    <!--</li>-->
    <!--</ul>-->
    <!--</li>-->
    <!--</ul>-->
    <!--</div>-->

    <!--<div class="yaml">-->
    <!--<pre v-html="getYaml(administration.file.inode)"></pre>-->
    <!--</div>-->

    <!--<div class="mt20">-->
    <!--<button class="btn btn-abus-blue-dark btn-sm" v-html="$t('box/admin/index')" @click="index()"></button>-->
    <!--<button class="btn btn-danger btn-sm" v-html="$t('box/admin/index_pdf')" @click="indexPDF()"></button>-->
    <!--<span class="lastIndexed" v-html="$t('box/admin/last_indexed') + ' ' + lastindexeddate"></span>-->
    <!--</div>-->

    <!--</b-tab>-->
    <!--<b-tab title="Berechtigungen">-->

    <!--<List-->
    <!--label="box/admin/allowed_groups"-->
    <!--description="box/admin/allowed_groups_description"-->
    <!--:elements="['WEB_INFORMATION_SALES_ADMIN', 'WEB_box']"-->
    <!--&gt;</List>-->

    <!--<Textfield-->
    <!--label="box/admin/allowed_groups"-->
    <!--description="box/admin/allowed_groups_description"-->
    <!--value="Hallo"-->
    <!--&gt;</Textfield>-->

    <!--<Date-->
    <!--label="box/admin/allowed_groups"-->
    <!--description="box/admin/allowed_groups_description"-->
    <!--value="01.01.2018"-->
    <!--&gt;</Date>-->

    <!--<Language-Selector-->
    <!--label="box/admin/languages"-->
    <!--description="box/admin/languages_description"-->
    <!--:value="['de', 'en']"-->
    <!--&gt;</Language-Selector>-->

    <!--<Select-->
    <!--label="box/admin/languages"-->
    <!--description="box/admin/languages_description"-->
    <!--value=""-->
    <!--&gt;</Select>-->

    <!--</b-tab>-->
    <!--<b-tab title="Spracheinstellungen">-->
    <!--Tab Contents 3-->
    <!--</b-tab>-->
    <!--<b-tab title="Sucheinstellungen">-->
    <!--Tab Contents 4-->
    <!--</b-tab>-->
    <!--<b-tab title="Anzeigeeinstellungen">-->
    <!--Tab Contents 5-->
    <!--</b-tab>-->
    <!--<b-tab title="Meta Angaben">-->
    <!--<Textfield-->
    <!--label="box/admin/meta/version"-->
    <!--description="box/admin/meta/version/description"-->
    <!--:inode="administration.file.inode"-->
    <!--category="meta"-->
    <!--setting="version"-->
    <!--&gt;</Textfield>-->

    <!--<div class="yaml">-->
    <!--<pre v-html="getYaml(administration.file.inode)"></pre>-->
    <!--</div>-->
    <!--</b-tab>-->
    <!--</b-tabs>-->
    <!--</b-card>-->

    <button class="btn btn-success btn-block mt10 mb10" type="button">Speichern und indizieren</button>
  </div>
</template>

<script lang="ts">
// var qs = require('qs');
//
// import Axios from 'axios';
import Vue from 'vue';
import Component from 'vue-class-component';
import { Prop } from 'vue-property-decorator';
import { Getter, Action } from 'vuex-class';

import moment from 'moment';

// import ModalEventBus from 'EventBuses/modal';

import Toggle from './inputs/toggle.vue';
import ThreeWaySwitch from './inputs/three-way-switch.vue';
import List from './inputs/list.vue';
import Textfield from './inputs/textfield.vue';
import Date from './inputs/date.vue';
import LanguageSelector from './inputs/languageSelector.vue';
import Select from './inputs/select.vue';

import BootstrapVue from 'bootstrap-vue';
Vue.use(BootstrapVue);

// import VueI18n from 'vue-i18n';
// Vue.use(VueI18n);
//
// const i18n = new VueI18n({
//     locale: <string>document.getElementsByTagName('html')[0].getAttribute('lang'),
//     messages: {
//         de: require('Translations/across/across.de_DE.json'),
//         en: require('Translations/across/across.en_GB.json'),
//         fr: require('Translations/across/across.fr_FR.json'),
//         es: require('Translations/across/across.es_ES.json')
//     }
// });

// index: 'Index directory structure',
//     indexPDF: 'Index PDF content',
//     indexModalText: 'Indexing was triggered. This may take a while, depending on the size.<br /><br />You will be notified by mail as soon as the indexing of the folder structure is completed.',
//     lastIndexed: 'Last indexed on',

// Index directory structure: Ordnerstruktur indizieren
// Indexing directory structure ...: Indiziere Ordnerstruktur ...
//

@Component({
  name: 'BoxAdminComponent',
  // i18n,
  components: {
    Toggle,
    ThreeWaySwitch,
    List,
    Textfield,
    Date,
    LanguageSelector,
    Select,
  },
})
export default class BoxAdmin extends Vue {
  @Prop({}) folder: any;
  @Prop({}) administration: any;

  @Getter('box/getAdminByInode') public getAdminByInode: (inode: number) => any;
  @Getter('box/getYaml') public getYaml: (inode: number) => any;

  @Action('box/setAdmin') setAdmin: any;

  lastindexeddate: string;

  public index(): void {
    this.createIndexTask(false);
  }

  public indexPDF(): void {
    this.createIndexTask(true);
  }

  private createIndexTask(pdf: boolean): void {
    // const url = '/taskrunner/create';
    //
    // let params = {
    //     'bundleName': 'ABUSBox',
    //     'task': 'crawler',
    //     'parameters': {
    //         'path': this.administration.file.filesystemPath,
    //         'pdf': pdf
    //     }
    // };
    //
    // Axios.post(url, qs.stringify(params), { headers: {'Content-Type': 'application/x-www-form-urlencoded'} })
    //     .then((response: any) => {
    //
    //         if (pdf) {
    //             ModalEventBus.$emit('showModal', {
    //                 'type': 'danger',
    //                 'title': i18n.t('box/admin/index_pdf'),
    //                 'message': i18n.t('box/admin/index_modal_text')
    //             });
    //         }
    //         else {
    //             ModalEventBus.$emit('showModal', {
    //                 'type': 'abus-blue-dark',
    //                 'title': i18n.t('box/admin/index'),
    //                 'message': i18n.t('box/admin/index_modal_text')
    //             });
    //         }
    //     })
    //     .catch((error: any) => {
    //         ModalEventBus.$emit('showModal', {'type': 'error', 'message': error.response.data});
    //     });
  }

  created() {
    moment.locale(<string>document.getElementsByTagName('html')[0].getAttribute('lang'));
    this.lastindexeddate = moment(this.administration.info.lastindexeddate * 1000)
      .format('LLL')
      .toString();
    this.setAdmin(this.administration);
  }
}
</script>

<style lang="scss">
#box .admin {
  .description {
    strong {
      font-weight: bold;
    }
  }
}
</style>

<style lang="scss">
.admin {
  margin-bottom: 20px;

  .tabs .tab-content {
    min-height: 100px;
  }
}

.yaml {
  background-color: #1f1f1f;
  padding: 10px;

  pre {
    color: #00ff00;
  }
}

hr {
  margin: 5px 0 12px 0;
}
</style>
