version: '2.4'

services:
  portal_db:
    extends:
      file: common.yml
      service: ${NAMEPREFIX}_db
    restart: always
    ports:
      - "62006:3306"

  portal_php-fpm:
    extends:
      file: common.yml
      service: ${NAMEPREFIX}_php-fpm
    depends_on:
      - ${NAMEPREFIX}_redis
    restart: always
    build:
      args:
        development: ${DEVELOPMENT}
#        http_proxy: ${PROXY}
#        https_proxy: ${PROXY}
        environment: ${ENVIRONMENT}
        xdebug_client_port: ${XDEBUG_CLIENT_PORT}
        xdebug_client_host: ${XDEBUG_CLIENT_HOST}
    environment:
      XDEBUG_CONFIG: ${XDEBUG_CONFIG}
      PHP_IDE_CONFIG: serverName=${NAMEPREFIX}
#      http_proxy: ${PROXY}
#      https_proxy: ${PROXY}
#      no_proxy: ${NO_PROXY}
    links:
      - ${NAMEPREFIX}_db

  portal_nginx:
    extends:
      file: common.yml
      service: ${NAMEPREFIX}_nginx
    restart: always
    depends_on:
      - ${NAMEPREFIX}_php-fpm
    build:
      args:
        development: ${DEVELOPMENT}
        environment: ${ENVIRONMENT}
#        http_proxy: ${PROXY}
#        https_proxy: ${PROXY}
    labels:
      - "traefik.http.routers.${NAMEPREFIX}-secure.rule=${HOSTS}"
    links:
      - ${NAMEPREFIX}_php-fpm

  portal_redis:
    extends:
      file: common.yml
      service: ${NAMEPREFIX}_redis
    restart: always

networks:
  portal-net:
    external: true
  traefik-net:
    external: true
  database-net:
    external: true
  elasticsearch-net:
    external: true