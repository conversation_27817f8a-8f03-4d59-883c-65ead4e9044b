version: '2.4'

services:
  portal_db:
    build:
      context: .
      dockerfile: ./mariadb/Dockerfile
    container_name: ${NAMEPREFIX}_db
    hostname: ${NAMEPREFIX}_db
    volumes:
      - /srv/${NAMEPREFIX}/data/mariadb:/var/lib/mysql
      - /srv/${NAMEPREFIX}/data/exchange:/data_exchange
      - /srv/${NAMEPREFIX}/logs/mysql:/var/logs/mysql
    env_file:
      - ./.secrets
    networks:
      - portal-net
      - database-net

  portal_php-fpm:
    build:
      context: .
      dockerfile: ./php-fpm/Dockerfile
    container_name: ${NAMEPREFIX}_php-fpm
    hostname: ${NAMEPREFIX}_php-fpm
    cpus: 3.4
    volumes:
      - /srv/${NAMEPREFIX}/data/www:/var/www/html
      - ${VUEFORM_DIST_PATH}:/var/www/html/public/vueform
      - /srv/${NAMEPREFIX}/data/servicedoc:/var/www/servicedoc
      - /srv/${NAMEPREFIX}/logs/php-fpm:/var/log/php
      - /srv/${NAMEPREFIX}/logs/cron:/var/log/cron
      - /srv/nextcloud/data/webdav:/var/webdav
    user: root
    extra_hosts:
      - "nextcloud.abus-kransysteme.de:************"
      - "${HOST}:${HOST_IP}"
    networks:
      - portal-net
      - elasticsearch-net

  portal_nginx:
    build:
      context: .
      dockerfile: ./nginx/Dockerfile
    container_name: ${NAMEPREFIX}_nginx
    hostname: ${NAMEPREFIX}_nginx
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${NAMEPREFIX}-secure.tls=true"
      - "traefik.http.routers.${NAMEPREFIX}-secure.entrypoints=${TRAEFIK_ENTRYPOINT}"
      - "traefik.http.routers.${NAMEPREFIX}-secure.service=${NAMEPREFIX}"
      - "traefik.http.services.${NAMEPREFIX}.loadbalancer.server.port=80"
      - "traefik.http.middlewares.${NAMEPREFIX}-headers.headers.customrequestheaders.X-Forwarded-Proto=https"
      - "traefik.http.routers.${NAMEPREFIX}-secure.middlewares=${NAMEPREFIX}-headers"
    volumes:
      - /srv/${NAMEPREFIX}/data/www:/var/www/html
      - ${VUEFORM_DIST_PATH}:/var/www/html/public/vueform
      - /srv/${NAMEPREFIX}/data/servicedoc:/var/www/servicedoc
      - /srv/${NAMEPREFIX}/logs/nginx:/var/log/nginx
    networks:
      - portal-net
      - traefik-net

  portal_redis:
    build:
      context: .
      dockerfile: ./redis/Dockerfile
    container_name: ${NAMEPREFIX}_redis
    hostname:  ${NAMEPREFIX}_redis
    volumes:
      - /srv/${NAMEPREFIX}/data/redis:/data
    networks:
      - portal-net
      - database-net
